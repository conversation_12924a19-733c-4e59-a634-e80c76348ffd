from flask import Flask, request, jsonify, send_from_directory, make_response
from flask_cors import CORS
import os, json, uuid, threading, tempfile
from datetime import datetime
from werkzeug.utils import secure_filename
from safety_analyzer import ConstructionSafetyAnalyzer

try:
    from reportlab.lib.pagesizes import A5
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

app = Flask(__name__)
CORS(app)

UPLOAD_FOLDER, RESULTS_FOLDER, DOCUMENTS_FOLDER = 'uploads', 'analysis_results', 'documents'
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv', 'wmv'}
app.config.update(UPLOAD_FOLDER=UPLOAD_FOLDER, MAX_CONTENT_LENGTH=100*1024*1024)

for folder in [UPLOAD_FOLDER, RESULTS_FOLDER, DOCUMENTS_FOLDER]:
    os.makedirs(folder, exist_ok=True)

videos_db, analysis_db, documents_db = {}, {}, {}

# Clear any existing data files on startup
def clear_existing_data():
    """Clear any existing analysis and document files AND reset in-memory databases"""
    global videos_db, analysis_db, documents_db

    try:
        # Clear in-memory databases first
        videos_db.clear()
        analysis_db.clear()
        documents_db.clear()

        # Clear analysis results files
        if os.path.exists(RESULTS_FOLDER):
            for file in os.listdir(RESULTS_FOLDER):
                if file.endswith('.json'):
                    os.remove(os.path.join(RESULTS_FOLDER, file))

        # Clear documents files
        if os.path.exists(DOCUMENTS_FOLDER):
            for file in os.listdir(DOCUMENTS_FOLDER):
                if file.endswith('.json'):
                    os.remove(os.path.join(DOCUMENTS_FOLDER, file))

        # Clear uploaded videos files
        if os.path.exists(UPLOAD_FOLDER):
            for file in os.listdir(UPLOAD_FOLDER):
                if file.endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv')):
                    try:
                        os.remove(os.path.join(UPLOAD_FOLDER, file))
                    except:
                        pass

        print("✅ Cleared ALL data (memory + files) for completely fresh start")
    except Exception as e:
        print(f"⚠️ Error clearing data: {e}")

# Clear data on startup
clear_existing_data()

# Initialize safety analyzer
analyzer = ConstructionSafetyAnalyzer()

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def analyze_video_async(video_path, video_id, video_title):
    """Analyze construction video for safety compliance"""
    try:
        print(f"🔍 Starting safety analysis for: {video_title}")
        
        # Perform construction safety analysis
        result = analyzer.analyze_construction_video(video_path, RESULTS_FOLDER)
        result['video_id'] = video_id
        result['video_title'] = video_title
        
        # Store analysis result
        analysis_db[video_id] = result
        
        # Update video status
        if video_id in videos_db:
            videos_db[video_id]['analyzed'] = True
            videos_db[video_id]['status'] = 'completed'
            videos_db[video_id]['analysis_completed'] = datetime.now().isoformat()
        
        # Generate safety report document
        generate_safety_document(result)
        
        print(f"✅ Safety analysis completed for: {video_title}")
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Error analyzing video: {error_msg}")

        # Update video status to show error
        if video_id in videos_db:
            videos_db[video_id]['analyzed'] = False
            videos_db[video_id]['status'] = 'failed'
            videos_db[video_id]['analysis_error'] = error_msg
            videos_db[video_id]['analysis_completed'] = datetime.now().isoformat()

        # Create a basic error analysis result
        error_result = {
            'video_id': video_id,
            'video_title': video_title,
            'error': error_msg,
            'analysis_status': 'failed',
            'timestamp': datetime.now().isoformat(),
            'total_workers': 0,
            'safety_violations': [],
            'compliance_score': 0,
            'recommendations': [
                f"Video analysis failed: {error_msg}",
                "Please check the video path/URL and try again",
                "For YouTube videos, ensure the URL is accessible and not private"
            ]
        }
        analysis_db[video_id] = error_result

def generate_pdf_document(analysis_result):
    """Generate A5 size PDF document for safety analysis"""
    if not PDF_AVAILABLE:
        return None

    # Create temporary PDF file
    temp_dir = tempfile.mkdtemp()
    pdf_filename = f"safety_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    pdf_path = os.path.join(temp_dir, pdf_filename)

    # Create PDF document with A5 size
    doc = SimpleDocTemplate(pdf_path, pagesize=A5,
                           rightMargin=0.5*inch, leftMargin=0.5*inch,
                           topMargin=0.5*inch, bottomMargin=0.5*inch)

    # Get styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle('CustomTitle', parent=styles['Heading1'],
                                fontSize=14, spaceAfter=12, alignment=TA_CENTER)
    heading_style = ParagraphStyle('CustomHeading', parent=styles['Heading2'],
                                  fontSize=11, spaceAfter=8, textColor=colors.darkblue)
    normal_style = ParagraphStyle('CustomNormal', parent=styles['Normal'],
                                 fontSize=9, spaceAfter=4)

    # Extract statistics
    workers_data = analysis_result.get('workers_data', [])
    total_workers = len(workers_data)

    workers_with_helmet = sum(1 for worker in workers_data if worker['safety_equipment']['helmet']['detected'])
    workers_without_helmet = total_workers - workers_with_helmet
    workers_with_shoes = sum(1 for worker in workers_data if worker['safety_equipment']['safety_shoes']['detected'])
    workers_without_shoes = total_workers - workers_with_shoes
    workers_with_uniform = sum(1 for worker in workers_data if worker['safety_equipment']['uniform']['detected'])
    workers_without_uniform = total_workers - workers_with_uniform

    # Build PDF content
    story = []

    # Title
    story.append(Paragraph("CONSTRUCTION SAFETY ANALYSIS REPORT", title_style))
    story.append(Spacer(1, 12))

    # Basic info
    story.append(Paragraph(f"<b>Video:</b> {analysis_result['video_title']}", normal_style))
    story.append(Paragraph(f"<b>Generated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", normal_style))
    story.append(Spacer(1, 12))

    # Worker Detection Summary
    story.append(Paragraph("WORKER DETECTION SUMMARY", heading_style))

    # Create table for statistics
    data = [
        ['Statistic', 'Count'],
        ['1. Total Workers Detected', str(total_workers)],
        ['2. Workers Wearing Helmet', str(workers_with_helmet)],
        ['3. Workers NOT Wearing Helmet', str(workers_without_helmet)],
        ['4. Workers Wearing Safety Shoes', str(workers_with_shoes)],
        ['5. Workers NOT Wearing Safety Shoes', str(workers_without_shoes)],
        ['6. Workers Wearing Uniform', str(workers_with_uniform)],
        ['7. Workers NOT Wearing Uniform', str(workers_without_uniform)],
    ]

    table = Table(data, colWidths=[2.5*inch, 1*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('FONTSIZE', (0, 1), (-1, -1), 9),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(table)
    story.append(Spacer(1, 12))

    # Compliance Rates
    story.append(Paragraph("SAFETY COMPLIANCE RATES", heading_style))
    helmet_compliance = (workers_with_helmet / total_workers * 100) if total_workers > 0 else 0
    shoes_compliance = (workers_with_shoes / total_workers * 100) if total_workers > 0 else 0
    uniform_compliance = (workers_with_uniform / total_workers * 100) if total_workers > 0 else 0

    story.append(Paragraph(f"• Helmet Compliance: {helmet_compliance:.1f}% ({workers_with_helmet}/{total_workers})", normal_style))
    story.append(Paragraph(f"• Safety Shoes Compliance: {shoes_compliance:.1f}% ({workers_with_shoes}/{total_workers})", normal_style))
    story.append(Paragraph(f"• Uniform Compliance: {uniform_compliance:.1f}% ({workers_with_uniform}/{total_workers})", normal_style))

    # Build PDF
    doc.build(story)

    return pdf_path

def generate_safety_document(analysis_result):
    """Generate comprehensive construction safety report document with detailed worker statistics"""
    doc_id = str(uuid.uuid4())

    # Extract detailed worker statistics
    workers_data = analysis_result.get('workers_data', [])
    total_workers = len(workers_data)

    # Calculate detailed statistics as requested
    workers_with_helmet = sum(1 for worker in workers_data if worker['safety_equipment']['helmet']['detected'])
    workers_without_helmet = total_workers - workers_with_helmet

    workers_with_shoes = sum(1 for worker in workers_data if worker['safety_equipment']['safety_shoes']['detected'])
    workers_without_shoes = total_workers - workers_with_shoes

    workers_with_uniform = sum(1 for worker in workers_data if worker['safety_equipment']['uniform']['detected'])
    workers_without_uniform = total_workers - workers_with_uniform

    # Calculate compliance percentages
    helmet_compliance = (workers_with_helmet / total_workers * 100) if total_workers > 0 else 0
    shoes_compliance = (workers_with_shoes / total_workers * 100) if total_workers > 0 else 0
    uniform_compliance = (workers_with_uniform / total_workers * 100) if total_workers > 0 else 0
    overall_compliance = (helmet_compliance + shoes_compliance + uniform_compliance) / 3

    # Create comprehensive safety report with list format as requested
    content = f"""CONSTRUCTION SAFETY ANALYSIS REPORT
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Video: {analysis_result['video_title']}
Analysis Date: {datetime.now().strftime('%B %d, %Y')}

=== WORKER DETECTION SUMMARY ===
1. Total Workers Detected: {total_workers}
2. Workers Wearing Helmet: {workers_with_helmet}
3. Workers NOT Wearing Helmet: {workers_without_helmet}
4. Workers Wearing Safety Shoes: {workers_with_shoes}
5. Workers NOT Wearing Safety Shoes: {workers_without_shoes}
6. Workers Wearing Uniform: {workers_with_uniform}
7. Workers NOT Wearing Uniform: {workers_without_uniform}

=== SAFETY COMPLIANCE RATES ===
• Helmet Compliance: {helmet_compliance:.1f}% ({workers_with_helmet}/{total_workers} workers)
• Safety Shoes Compliance: {shoes_compliance:.1f}% ({workers_with_shoes}/{total_workers} workers)
• Uniform Compliance: {uniform_compliance:.1f}% ({workers_with_uniform}/{total_workers} workers)
• Overall Safety Compliance: {overall_compliance:.1f}%

=== SAFETY VIOLATIONS SUMMARY ===

HELMET VIOLATIONS:
- Workers Without Helmet: {workers_without_helmet} out of {total_workers}
- Violation Rate: {(workers_without_helmet/total_workers*100):.1f}%
- Status: {'CRITICAL' if workers_without_helmet > total_workers*0.3 else 'MODERATE' if workers_without_helmet > 0 else 'COMPLIANT'}

UNIFORM VIOLATIONS:
- Workers Without Uniform: {workers_without_uniform} out of {total_workers}
- Violation Rate: {(workers_without_uniform/total_workers*100):.1f}%
- Status: {'CRITICAL' if workers_without_uniform > total_workers*0.3 else 'MODERATE' if workers_without_uniform > 0 else 'COMPLIANT'}

SAFETY SHOES VIOLATIONS:
- Workers Without Safety Shoes: {workers_without_shoes} out of {total_workers}
- Violation Rate: {(workers_without_shoes/total_workers*100):.1f}%
- Status: {'CRITICAL' if workers_without_shoes > total_workers*0.3 else 'MODERATE' if workers_without_shoes > 0 else 'COMPLIANT'}

=== RISK ASSESSMENT ===
High Risk Workers: {analysis_result.get('risk_assessment', {}).get('high_risk_workers', 0)}
Medium Risk Workers: {analysis_result.get('risk_assessment', {}).get('medium_risk_workers', 0)}
Low Risk Workers: {analysis_result.get('risk_assessment', {}).get('low_risk_workers', 0)}
Overall Risk Level: {analysis_result.get('risk_assessment', {}).get('overall_risk_level', 'Unknown').upper()}

=== SAFETY RECOMMENDATIONS ===
"""

    # Add recommendations based on violations
    recommendations = []
    if workers_without_helmet > 0:
        recommendations.append(f"HIGH PRIORITY - Helmet Safety: {workers_without_helmet} workers need helmets")
    if workers_without_uniform > 0:
        recommendations.append(f"MEDIUM PRIORITY - Uniform Safety: {workers_without_uniform} workers need proper uniforms")
    if workers_without_shoes > 0:
        recommendations.append(f"HIGH PRIORITY - Foot Safety: {workers_without_shoes} workers need safety shoes")

    if recommendations:
        for rec in recommendations:
            content += f"\n• {rec}\n"
    else:
        content += "\n• All workers are compliant with safety requirements - Excellent work!\n"

    # Add detailed worker analysis
    content += f"""

=== DETAILED WORKER ANALYSIS ==="""

    for worker in analysis_result.get('workers_data', []):
        # Safety status indicators
        helmet_status = "✓" if worker['safety_equipment']['helmet']['detected'] else "✗"
        uniform_status = "✓" if worker['safety_equipment']['uniform']['detected'] else "✗"
        shoes_status = "✓" if worker['safety_equipment']['safety_shoes']['detected'] else "✗"
        face_status = "✓" if worker['face_detected'] else "✗"

        content += f"""

{worker['worker_id']}:
- Face Detection: {face_status} {'Detected' if worker['face_detected'] else 'Not Detected'}"""

        if 'face_detection_rate' in worker:
            content += f" ({worker['face_detection_rate']}% of frames)"

        content += f"""
- Helmet: {helmet_status} {'Present' if worker['safety_equipment']['helmet']['detected'] else 'MISSING'}"""

        if 'detection_rate' in worker['safety_equipment']['helmet']:
            content += f" ({worker['safety_equipment']['helmet']['detection_rate']}% detection rate)"

        if worker['safety_equipment']['helmet']['color']:
            content += f" - Color: {worker['safety_equipment']['helmet']['color'].title()}"

        content += f"""
- Uniform: {uniform_status} {'Proper' if worker['safety_equipment']['uniform']['detected'] else 'IMPROPER'}"""

        if 'detection_rate' in worker['safety_equipment']['uniform']:
            content += f" ({worker['safety_equipment']['uniform']['detection_rate']}% detection rate)"

        content += f"""
- Safety Shoes: {shoes_status} {'Present' if worker['safety_equipment']['safety_shoes']['detected'] else 'MISSING'}"""

        if 'detection_rate' in worker['safety_equipment']['safety_shoes']:
            content += f" ({worker['safety_equipment']['safety_shoes']['detection_rate']}% detection rate)"

        content += f"""
- Risk Level: {worker['risk_level'].upper()}
- Violations: {', '.join(worker['violations']) if worker['violations'] else 'None - Fully Compliant'}"""

        if 'total_detections' in worker:
            content += f"""
- Detection Stats: Observed in {worker['total_detections']} frames"""

    # Add analysis metadata
    content += f"""

=== ANALYSIS METADATA ===
Analysis ID: {analysis_result['analysis_id']}
Processing Date: {analysis_result['analysis_date']}
Video Duration: {analysis_result['video_info']['duration']} seconds
Total Frames: {analysis_result['video_info']['total_frames']}
FPS: {analysis_result['video_info']['fps']}
Analysis Method: Deep Computer Vision Analysis
Detection Models: Face + Body + Color Analysis

Report generated by VideoVault Construction Safety Monitor v2.0
Advanced AI-Powered Safety Compliance Analysis System
"""
    
    # Generate PDF version if available
    pdf_path = None
    if PDF_AVAILABLE:
        try:
            pdf_path = generate_pdf_document(analysis_result)
            if pdf_path:
                # Move PDF to documents folder
                pdf_filename = f"{doc_id}.pdf"
                final_pdf_path = os.path.join(DOCUMENTS_FOLDER, pdf_filename)
                os.rename(pdf_path, final_pdf_path)
                pdf_path = final_pdf_path
        except Exception as e:
            print(f"⚠️ Could not generate PDF: {e}")
            pdf_path = None

    # Save document
    document = {
        'id': doc_id,
        'title': f"Safety Analysis Report - {analysis_result['video_title']}",
        'content': content,
        'video_id': analysis_result['video_id'],
        'created_date': datetime.now().isoformat(),
        'type': 'safety_report',
        'analysis_data': analysis_result,
        'has_pdf': pdf_path is not None,
        'pdf_path': pdf_path
    }

    # Save to file
    doc_file = os.path.join(DOCUMENTS_FOLDER, f"{doc_id}.json")
    with open(doc_file, 'w') as f:
        json.dump(document, f, indent=2)

    documents_db[doc_id] = document
    return document

# Routes
@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    return send_from_directory('.', filename)

@app.route('/api/upload', methods=['POST'])
def upload_video():
    """Upload video for safety analysis - supports both file upload and video path"""
    try:
        # Check if it's a JSON request (path upload) or form data (file upload)
        if request.content_type and 'application/json' in request.content_type:
            # Handle video path upload
            data = request.get_json()
            title = data.get('title', 'Untitled Video')
            video_path = data.get('video_path', '')

            if not video_path:
                return jsonify({'error': 'No video path provided'}), 400

            # Validate path exists (for local files)
            if not video_path.startswith(('http://', 'https://')) and not os.path.exists(video_path):
                return jsonify({'error': 'Video file not found at specified path'}), 400

            # Generate video ID and store info
            video_id = str(uuid.uuid4())
            filename = os.path.basename(video_path) if video_path else 'video_from_path'

            video_data = {
                'id': video_id,
                'title': title,
                'filename': filename,
                'file_path': video_path,
                'upload_date': datetime.now().isoformat(),
                'analyzed': False,
                'status': 'analyzing',
                'upload_method': 'path'
            }
            videos_db[video_id] = video_data

            # Start analysis in background
            thread = threading.Thread(
                target=analyze_video_async,
                args=(video_path, video_id, title)
            )
            thread.daemon = True
            thread.start()

            return jsonify({
                'success': True,
                'video_id': video_id,
                'message': 'Video path received. Analysis started.'
            })

        else:
            # Check if this is a path upload sent as form data
            upload_method = request.form.get('upload_method', 'file')

            if upload_method == 'path':
                # Handle path upload sent as form data
                title = request.form.get('title', 'Untitled Video')
                video_path = request.form.get('video_path', '')

                if not video_path:
                    return jsonify({'error': 'No video path provided'}), 400

                # Validate path exists (for local files)
                if not video_path.startswith(('http://', 'https://')) and not os.path.exists(video_path):
                    return jsonify({'error': 'Video file not found at specified path'}), 400

                # Generate video ID and store info
                video_id = str(uuid.uuid4())
                filename = os.path.basename(video_path) if video_path else 'video_from_path'

                video_data = {
                    'id': video_id,
                    'title': title,
                    'filename': filename,
                    'file_path': video_path,
                    'upload_date': datetime.now().isoformat(),
                    'analyzed': False,
                    'status': 'analyzing',
                    'upload_method': 'path'
                }
                videos_db[video_id] = video_data

                # Start analysis in background
                thread = threading.Thread(
                    target=analyze_video_async,
                    args=(video_path, video_id, title)
                )
                thread.daemon = True
                thread.start()

                return jsonify({
                    'success': True,
                    'video_id': video_id,
                    'message': 'Video path received. Analysis started.'
                })

            # Handle file upload
            if 'video' not in request.files:
                return jsonify({'error': 'No video file provided'}), 400

            file = request.files['video']
            title = request.form.get('title', 'Untitled Video')

            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400

            if not allowed_file(file.filename):
                return jsonify({'error': 'Invalid file type'}), 400

            # Save file
            filename = secure_filename(file.filename)
            video_id = str(uuid.uuid4())
            file_path = os.path.join(UPLOAD_FOLDER, f"{video_id}_{filename}")
            file.save(file_path)

            # Store video info
            video_data = {
                'id': video_id,
                'title': title,
                'filename': filename,
                'file_path': file_path,
                'upload_date': datetime.now().isoformat(),
                'analyzed': False,
                'status': 'analyzing',
                'upload_method': 'file'
            }
            videos_db[video_id] = video_data

            # Start analysis in background
            thread = threading.Thread(
                target=analyze_video_async,
                args=(file_path, video_id, title)
            )
            thread.daemon = True
            thread.start()

            return jsonify({
                'success': True,
                'video_id': video_id,
                'message': 'Video uploaded successfully. Analysis started.'
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/videos', methods=['GET'])
def get_videos():
    """Get all uploaded videos"""
    return jsonify(list(videos_db.values()))

@app.route('/api/analysis/<video_id>', methods=['GET'])
def get_analysis(video_id):
    """Get analysis results for a specific video"""
    if video_id in analysis_db:
        return jsonify(analysis_db[video_id])
    return jsonify({'error': 'Analysis not found'}), 404

@app.route('/api/documents', methods=['GET'])
def get_documents():
    """Get all documents"""
    return jsonify(list(documents_db.values()))

@app.route('/api/documents/<doc_id>', methods=['GET'])
def get_document(doc_id):
    """Get specific document"""
    if doc_id in documents_db:
        return jsonify(documents_db[doc_id])
    return jsonify({'error': 'Document not found'}), 404

@app.route('/api/documents/<doc_id>/download')
def download_document(doc_id):
    """Download a document as a text file"""
    if doc_id not in documents_db:
        return jsonify({'error': 'Document not found'}), 404

    document = documents_db[doc_id]

    # Create response with document content
    response = make_response(document['content'])
    response.headers['Content-Type'] = 'text/plain; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename="{document["title"]}.txt"'

    return response

@app.route('/api/documents/<doc_id>/download-pdf')
def download_document_pdf(doc_id):
    """Download a document as a PDF file"""
    if doc_id not in documents_db:
        return jsonify({'error': 'Document not found'}), 404

    document = documents_db[doc_id]

    if not document.get('has_pdf') or not document.get('pdf_path'):
        return jsonify({'error': 'PDF not available for this document'}), 404

    pdf_path = document['pdf_path']
    if not os.path.exists(pdf_path):
        return jsonify({'error': 'PDF file not found'}), 404

    return send_from_directory(
        os.path.dirname(pdf_path),
        os.path.basename(pdf_path),
        as_attachment=True,
        download_name=f"{document['title']}.pdf"
    )

@app.route('/api/documents/<doc_id>/view-pdf')
def view_document_pdf(doc_id):
    """View a document PDF in browser"""
    if doc_id not in documents_db:
        return jsonify({'error': 'Document not found'}), 404

    document = documents_db[doc_id]

    if not document.get('has_pdf') or not document.get('pdf_path'):
        return jsonify({'error': 'PDF not available for this document'}), 404

    pdf_path = document['pdf_path']
    if not os.path.exists(pdf_path):
        return jsonify({'error': 'PDF file not found'}), 404

    return send_from_directory(
        os.path.dirname(pdf_path),
        os.path.basename(pdf_path),
        as_attachment=False
    )

@app.route('/api/videos/<video_id>', methods=['DELETE'])
def delete_video(video_id):
    """Delete a video and its associated data"""
    try:
        # Check if video exists
        if video_id not in videos_db:
            return jsonify({'success': False, 'error': 'Video not found'}), 404

        video_data = videos_db[video_id]

        # Delete video file if it exists
        if 'filepath' in video_data and os.path.exists(video_data['filepath']):
            try:
                os.remove(video_data['filepath'])
                print(f"🗑️ Deleted video file: {video_data['filepath']}")
            except Exception as e:
                print(f"⚠️ Could not delete video file: {e}")

        # Delete analysis results if they exist
        if video_id in analysis_db:
            del analysis_db[video_id]
            print(f"🗑️ Deleted analysis data for video: {video_id}")

        # Delete associated documents
        docs_to_delete = []
        for doc_id, doc_data in documents_db.items():
            if doc_data.get('video_id') == video_id:
                docs_to_delete.append(doc_id)

        for doc_id in docs_to_delete:
            # Delete document file
            doc_file = os.path.join(DOCUMENTS_FOLDER, f"{doc_id}.json")
            if os.path.exists(doc_file):
                try:
                    os.remove(doc_file)
                    print(f"🗑️ Deleted document file: {doc_file}")
                except Exception as e:
                    print(f"⚠️ Could not delete document file: {e}")

            # Remove from memory
            del documents_db[doc_id]
            print(f"🗑️ Deleted document: {doc_id}")

        # Delete from videos database
        del videos_db[video_id]
        print(f"🗑️ Deleted video: {video_data['title']}")

        return jsonify({
            'success': True,
            'message': f'Video "{video_data["title"]}" and associated data deleted successfully'
        })

    except Exception as e:
        print(f"❌ Error deleting video: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/documents/<doc_id>', methods=['DELETE'])
def delete_document(doc_id):
    """Delete a specific document"""
    try:
        # Check if document exists
        if doc_id not in documents_db:
            return jsonify({'success': False, 'error': 'Document not found'}), 404

        doc_data = documents_db[doc_id]

        # Delete document file
        doc_file = os.path.join(DOCUMENTS_FOLDER, f"{doc_id}.json")
        if os.path.exists(doc_file):
            try:
                os.remove(doc_file)
                print(f"🗑️ Deleted document file: {doc_file}")
            except Exception as e:
                print(f"⚠️ Could not delete document file: {e}")

        # Delete from memory
        del documents_db[doc_id]
        print(f"🗑️ Deleted document: {doc_data['title']}")

        return jsonify({
            'success': True,
            'message': f'Document "{doc_data["title"]}" deleted successfully'
        })

    except Exception as e:
        print(f"❌ Error deleting document: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/videos/<video_id>/retry', methods=['POST'])
def retry_video_analysis(video_id):
    """Retry analysis for a failed video"""
    try:
        # Check if video exists
        if video_id not in videos_db:
            return jsonify({'success': False, 'error': 'Video not found'}), 404

        video_data = videos_db[video_id]

        # Reset video status
        videos_db[video_id]['analyzed'] = False
        videos_db[video_id]['status'] = 'analyzing'
        videos_db[video_id]['analysis_error'] = None

        # Remove any existing analysis data
        if video_id in analysis_db:
            del analysis_db[video_id]

        # Get video path
        video_path = video_data.get('file_path') or video_data.get('filepath')
        if not video_path:
            return jsonify({'success': False, 'error': 'Video path not found'}), 400

        # Start analysis in background
        thread = threading.Thread(
            target=analyze_video_async,
            args=(video_path, video_id, video_data['title'])
        )
        thread.daemon = True
        thread.start()

        print(f"🔄 Retrying analysis for video: {video_data['title']}")

        return jsonify({
            'success': True,
            'message': f'Analysis restarted for "{video_data["title"]}"'
        })

    except Exception as e:
        print(f"❌ Error retrying video analysis: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/clear-all-data', methods=['POST'])
def clear_all_data():
    """Clear all data - for testing/reset purposes"""
    try:
        clear_existing_data()
        return jsonify({
            'success': True,
            'message': 'All data cleared successfully',
            'videos_count': len(videos_db),
            'analysis_count': len(analysis_db),
            'documents_count': len(documents_db)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Get dashboard statistics - returns accurate counts only"""
    # Count only actual uploaded videos
    total_videos = len([v for v in videos_db.values() if v.get('id')])
    analyzed_videos = sum(1 for v in videos_db.values() if v.get('analyzed', False))
    total_documents = len([d for d in documents_db.values() if d.get('id')])

    # Calculate safety statistics only from actual analysis data
    total_workers = 0
    total_violations = 0

    for analysis in analysis_db.values():
        if analysis and isinstance(analysis, dict):
            try:
                detection_summary = analysis.get('detection_summary', {})
                total_workers += detection_summary.get('total_workers_detected', 0)

                violations = analysis.get('safety_violations', {})
                helmet_count = violations.get('helmet_violations', {}).get('count', 0)
                uniform_count = violations.get('uniform_violations', {}).get('count', 0)
                shoes_count = violations.get('shoes_violations', {}).get('count', 0)
                total_violations += (helmet_count + uniform_count + shoes_count)
            except (KeyError, TypeError):
                # Skip invalid analysis data
                continue

    # Calculate compliance rate
    compliance_rate = 100
    if total_workers > 0:
        total_possible_violations = total_workers * 3  # 3 safety items per worker
        compliance_rate = round(((total_possible_violations - total_violations) / total_possible_violations * 100), 1)

    return jsonify({
        'total_videos': total_videos,
        'analyzed_videos': analyzed_videos,
        'total_documents': total_documents,
        'total_workers_detected': total_workers,
        'total_safety_violations': total_violations,
        'compliance_rate': compliance_rate
    })

if __name__ == '__main__':
    print("🏗️ Construction Safety Monitoring System")
    print("=" * 50)
    print("🚀 Server starting on http://localhost:5000")
    print("📊 Upload construction videos for safety analysis")
    print("⚠️  Detects: Helmets, Uniforms, Safety Shoes, Faces")
    print("=" * 50)
    
    try:
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped. Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
