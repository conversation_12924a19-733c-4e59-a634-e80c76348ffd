/* Construction Safety Monitor - Dashboard Styles */

:root{
    --primary-500:#f97316;
    --primary-600:#ea580c;
    --primary-700:#c2410c;
    --safety-green:#16a34a;
    --warning-orange:#f59e0b;
    --danger-red:#dc2626;
    --neutral-0:#ffffff;
    --neutral-50:#f8fafc;
    --neutral-100:#f1f5f9;
    --neutral-200:#e2e8f0;
    --neutral-300:#cbd5e1;
    --neutral-400:#94a3b8;
    --neutral-500:#64748b;
    --neutral-600:#475569;
    --neutral-700:#334155;
    --neutral-800:#1e293b;
    --neutral-900:#0f172a;
    --font-sans:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;
    --text-sm:0.875rem;
    --text-base:1rem;
    --text-lg:1.125rem;
    --text-xl:1.25rem;
    --text-2xl:1.5rem;
    --space-1:0.25rem;
    --space-2:0.5rem;
    --space-3:0.75rem;
    --space-4:1rem;
    --space-6:1.5rem;
    --space-8:2rem;
    --sidebar-width:16rem;
    --sidebar-collapsed:4rem;
    --radius-md:0.5rem;
    --radius-lg:0.75rem;
    --shadow-sm:0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1);
    --transition:200ms ease
}

* { 
    margin: 0; 
    padding: 0; 
    box-sizing: border-box; 
}

body { 
    font-family: var(--font-sans); 
    font-size: var(--text-sm); 
    line-height: 1.5; 
    color: var(--neutral-700); 
    background: var(--neutral-50); 
}

.hidden { display: none !important; }
.flex { display: flex !important; }
.items-center { align-items: center !important; }
.justify-between { justify-content: space-between !important; }
.gap-2 { gap: var(--space-2) !important; }
.gap-4 { gap: var(--space-4) !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

/* Login Page */
.login-layout { 
    min-height: 100vh; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    padding: var(--space-6); 
    background: linear-gradient(135deg, #fff7ed 0%, var(--neutral-100) 100%); 
}

.login-card { 
    background: var(--neutral-0); 
    border-radius: var(--radius-lg); 
    box-shadow: var(--shadow-lg); 
    overflow: hidden; 
    width: 100%; 
    max-width: 420px; 
}

.login-header { 
    padding: var(--space-8); 
    text-align: center; 
    background: linear-gradient(180deg, var(--neutral-0) 0%, var(--neutral-50) 100%); 
}

.brand-logo { 
    height: 40px; 
    margin-bottom: var(--space-6); 
}

.welcome-title { 
    font-size: var(--text-2xl); 
    font-weight: 700; 
    color: var(--neutral-900); 
    margin-bottom: var(--space-2); 
}

.welcome-subtitle { 
    color: var(--neutral-500); 
    line-height: 1.6; 
}

.login-form { 
    padding: var(--space-8); 
}

.form-group { 
    margin-bottom: var(--space-6); 
}

.form-label { 
    display: block; 
    font-weight: 500; 
    color: var(--neutral-700); 
    margin-bottom: var(--space-2); 
}

.input-wrapper { 
    position: relative; 
}

.input-icon { 
    position: absolute; 
    left: var(--space-3); 
    top: 50%; 
    transform: translateY(-50%); 
    color: var(--neutral-400); 
    pointer-events: none; 
}

.form-input { 
    width: 100%; 
    padding: var(--space-3) var(--space-3) var(--space-3) 2.5rem; 
    border: 1px solid var(--neutral-300); 
    border-radius: var(--radius-lg); 
    transition: all var(--transition); 
    min-height: 2.75rem; 
}

.form-input:focus { 
    outline: none; 
    border-color: var(--primary-500); 
    box-shadow: 0 0 0 3px rgb(249 115 22 / 0.1); 
}

.password-toggle { 
    position: absolute; 
    right: var(--space-3); 
    top: 50%; 
    transform: translateY(-50%); 
    background: none; 
    border: none; 
    color: var(--neutral-400); 
    cursor: pointer; 
    padding: var(--space-1); 
    border-radius: var(--radius-md); 
}

.submit-btn { 
    width: 100%; 
    background: var(--primary-600); 
    color: var(--neutral-0); 
    border: none; 
    padding: var(--space-4); 
    border-radius: var(--radius-lg); 
    font-weight: 500; 
    cursor: pointer; 
    transition: all var(--transition); 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    gap: var(--space-2); 
    margin-top: var(--space-8); 
}

.submit-btn:hover { 
    background: var(--primary-700); 
    transform: translateY(-1px); 
    box-shadow: var(--shadow-lg); 
}

.login-footer { 
    padding: var(--space-6) var(--space-8); 
    text-align: center; 
    background: var(--neutral-50); 
    border-top: 1px solid var(--neutral-200); 
}

.footer-text { 
    font-size: 0.75rem; 
    color: var(--neutral-500); 
}

.footer-link { 
    color: var(--primary-600); 
    text-decoration: none; 
    font-weight: 500; 
}

.header-buttons { 
    display: flex; 
    align-items: center; 
    gap: var(--space-4); 
}

.logout-btn-header { 
    background: var(--danger-red); 
    color: var(--neutral-0); 
    border: none; 
    padding: var(--space-2) var(--space-4); 
    border-radius: var(--radius-md); 
    cursor: pointer; 
    display: flex; 
    align-items: center; 
    gap: var(--space-2); 
    font-weight: 500; 
    transition: all var(--transition); 
}

.logout-btn-header:hover { 
    background: #b91c1c; 
    transform: translateY(-1px); 
    box-shadow: var(--shadow-lg); 
}

/* Dashboard */
.app-layout { 
    display: flex; 
    height: 100vh; 
    overflow: hidden; 
}

.sidebar { 
    width: var(--sidebar-width); 
    background: var(--neutral-900); 
    color: var(--neutral-100); 
    display: flex; 
    flex-direction: column; 
    transition: width var(--transition); 
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed);
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--neutral-800);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 5rem;
    background: linear-gradient(135deg, var(--neutral-900) 0%, var(--neutral-800) 100%);
}

.logo {
    height: 40px;
    transition: opacity var(--transition);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.sidebar.collapsed .logo {
    opacity: 0;
    width: 0;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--neutral-400);
    cursor: pointer;
    padding: var(--space-3);
    border-radius: var(--radius-md);
    transition: all var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: var(--neutral-800);
    color: var(--neutral-100);
    transform: scale(1.05);
}

.sidebar-nav {
    flex: 1;
    padding: var(--space-6) 0;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--neutral-400);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 var(--space-6);
    margin-bottom: var(--space-4);
}

.sidebar.collapsed .nav-section-title {
    display: none;
}

.nav-list {
    list-style: none;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-6);
    color: var(--neutral-400);
    text-decoration: none;
    transition: all var(--transition);
    position: relative;
}

.nav-link:hover {
    background: var(--neutral-800);
    color: var(--neutral-100);
}

.nav-link.active {
    background: var(--primary-600);
    color: var(--neutral-0);
}

.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--space-3);
    flex-shrink: 0;
}

.sidebar.collapsed .nav-icon {
    margin-right: 0;
}

.nav-text {
    font-weight: 500;
    transition: opacity var(--transition);
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--neutral-800);
}

.logout-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.logout-btn {
    background: var(--neutral-800);
    border: 1px solid var(--neutral-700);
    color: var(--neutral-300);
    cursor: pointer;
    padding: var(--space-3);
    border-radius: var(--radius-md);
    transition: all var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.logout-btn:hover {
    background: var(--danger-red);
    color: var(--neutral-0);
    border-color: var(--danger-red);
    transform: scale(1.05);
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-header {
    background: linear-gradient(135deg, var(--neutral-0) 0%, var(--neutral-50) 100%);
    border-bottom: 1px solid var(--neutral-200);
    box-shadow: var(--shadow-sm);
    position: relative;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-8) var(--space-8);
    position: relative;
}

.back-button {
    position: fixed;
    left: var(--space-6);
    top: 80px;
    background: var(--primary-600);
    border: 2px solid var(--primary-700);
    color: var(--neutral-0);
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    display: none;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    z-index: 1001;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
    font-weight: bold;
}

.back-button:hover {
    background: var(--primary-700);
    border-color: var(--primary-800);
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
}

.back-button.show {
    display: flex;
}

/* When sidebar is expanded - normal position */
.back-button.show {
    left: calc(var(--sidebar-width) + var(--space-6));
}

/* When sidebar is collapsed - move to collapsed position */
.sidebar.collapsed ~ * .back-button.show {
    left: calc(var(--sidebar-collapsed) + var(--space-6)) !important;
    background: var(--primary-500) !important;
    width: 56px !important;
    height: 56px !important;
    box-shadow: 0 8px 24px rgba(249, 115, 22, 0.5) !important;
    animation: pulseGlow 2s infinite;
    border: 3px solid var(--primary-700) !important;
}

.sidebar.collapsed ~ * .back-button.show:hover {
    background: var(--primary-600) !important;
    transform: scale(1.2) !important;
    box-shadow: 0 12px 32px rgba(249, 115, 22, 0.6) !important;
    animation: none;
}

/* Pulse animation for better visibility */
@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 8px 24px rgba(249, 115, 22, 0.5);
    }
    50% {
        box-shadow: 0 8px 24px rgba(249, 115, 22, 0.7), 0 0 0 6px rgba(249, 115, 22, 0.3);
        transform: scale(1.05);
    }
}

/* Adjust page title and subtitle when sidebar is collapsed */
.sidebar.collapsed ~ .main-content .page-title {
    margin-left: 140px !important; /* Fixed margin when collapsed */
}

.sidebar.collapsed ~ .main-content .page-subtitle {
    margin-left: 140px !important; /* Fixed margin when collapsed */
}

.page-title {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--neutral-900);
    margin-bottom: var(--space-1);
    margin-left: 80px; /* Space for back button */
    transition: margin-left var(--transition);
}

.page-subtitle {
    color: var(--neutral-500);
    margin-left: 80px; /* Space for back button */
    transition: margin-left var(--transition);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition);
    text-decoration: none;
    min-height: 2.5rem;
}

.btn-primary {
    background: var(--primary-600);
    color: var(--neutral-0);
}

.btn-primary:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.content-section {
    flex: 1;
    padding: var(--space-8) var(--space-8) var(--space-8) var(--space-8);
    overflow-y: auto;
    display: none;
    background: var(--neutral-50);
}

.content-section.active {
    display: block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-card {
    background: var(--neutral-0);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon-primary {
    background: #fff7ed;
    color: var(--primary-600);
}

.stat-icon-success {
    background: rgb(22 163 74 / 0.1);
    color: var(--safety-green);
}

.stat-icon-warning {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-orange);
}

.stat-value {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--neutral-900);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-weight: 500;
    color: var(--neutral-700);
    margin-bottom: var(--space-1);
}

.stat-description {
    font-size: var(--text-sm);
    color: var(--neutral-500);
}

.activity-title {
    margin-bottom: var(--space-4);
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--neutral-900);
}

/* Delete button styles */
.delete-btn:hover {
    background: #b91c1c !important;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Analysis completion modal styles */
.analysis-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.analysis-modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 2rem;
    border-radius: 1rem;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.modal-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.modal-subtitle {
    color: var(--neutral-600);
    font-size: 0.875rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.modal-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.modal-btn-primary {
    background: var(--primary-600);
    color: white;
    border: none;
}

.modal-btn-primary:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
}

.modal-btn-secondary {
    background: var(--neutral-100);
    color: var(--neutral-700);
    border: 1px solid var(--neutral-300);
}

.modal-btn-secondary:hover {
    background: var(--neutral-200);
    transform: translateY(-1px);
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: var(--space-4);
    left: var(--space-4);
    background: var(--primary-600);
    border: none;
    color: var(--neutral-0);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    cursor: pointer;
    z-index: 1002;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all var(--transition);
}

.mobile-menu-toggle:hover {
    background: var(--primary-700);
    transform: scale(1.05);
}

/* Responsive */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sidebar {
        position: fixed;
        left: -100%;
        width: var(--sidebar-width);
        height: 100vh;
        z-index: 1000;
        transition: left var(--transition);
    }

    .sidebar.open {
        left: 0;
    }

    .header-content {
        padding: var(--space-4) var(--space-6);
    }

    .content-section {
        padding: var(--space-6);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: var(--text-xl);
        margin-left: 60px;
    }

    .page-subtitle {
        margin-left: 60px;
    }

    .back-button {
        left: var(--space-4);
        width: 44px;
        height: 44px;
    }

    /* Mobile sidebar collapsed adjustments */
    .sidebar.collapsed ~ .main-content .back-button {
        left: 20px !important;
        width: 50px !important;
        height: 50px !important;
    }

    .sidebar.collapsed ~ .main-content .page-title {
        margin-left: 90px !important;
    }

    .sidebar.collapsed ~ .main-content .page-subtitle {
        margin-left: 90px !important;
    }
}

/* Modal body styles */
.modal-body-text {
    text-align: center;
    color: var(--neutral-600);
    margin-bottom: 1rem;
}

.modal-info-container {
    background: var(--neutral-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.modal-info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.modal-info-row:last-child {
    margin-bottom: 0;
}

.modal-info-label {
    color: var(--neutral-600);
}

.modal-info-value {
    font-weight: 500;
}

/* Common utility classes for dynamic content */
.text-center {
    text-align: center;
}

.mb-1 {
    margin-bottom: 1rem;
}

.mb-2 {
    margin-bottom: 2rem;
}

.mb-half {
    margin-bottom: 0.5rem;
}

.mb-quarter {
    margin-bottom: 0.25rem;
}

.flex {
    display: flex;
}

.flex-between {
    display: flex;
    justify-content: space-between;
}

.flex-center {
    display: flex;
    align-items: center;
}

.flex-gap {
    gap: 1rem;
}

.flex-gap-half {
    gap: 0.5rem;
}

.w-full {
    width: 100%;
}

.hidden {
    display: none;
}

.text-neutral-500 {
    color: var(--neutral-500);
}

.text-neutral-600 {
    color: var(--neutral-600);
}

.text-sm {
    font-size: 0.875rem;
}

.text-xs {
    font-size: 0.75rem;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.p-3 {
    padding: 3rem;
}

.p-1 {
    padding: 1rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-success {
    background: rgb(22 163 74 / 0.1);
    color: var(--safety-green);
}

.status-warning {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-orange);
}

.status-error {
    background: rgb(239 68 68 / 0.1);
    color: var(--danger-red);
}

.btn-small {
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    border: none;
}

.btn-danger {
    background: var(--danger-red);
    color: white;
}

.btn-success {
    background: #059669;
    color: white;
}

.btn-primary-small {
    background: var(--primary-600);
    color: white;
}

.icon-container {
    width: 48px;
    height: 48px;
    background: #fff7ed;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-bottom: 1px solid var(--neutral-200);
}

.activity-icon {
    width: 32px;
    height: 32px;
    background: #fff7ed;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
}
