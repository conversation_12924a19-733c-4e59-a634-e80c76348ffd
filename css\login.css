/* Construction Safety Monitor - Login Page Styles */

:root {
    --primary-500: #f97316; 
    --primary-600: #ea580c; 
    --primary-700: #c2410c;
    --neutral-0: #ffffff; 
    --neutral-50: #f8fafc; 
    --neutral-100: #f1f5f9;
    --neutral-200: #e2e8f0; 
    --neutral-300: #cbd5e1; 
    --neutral-400: #94a3b8;
    --neutral-500: #64748b; 
    --neutral-700: #334155; 
    --neutral-900: #0f172a;
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --text-sm: 0.875rem; 
    --text-2xl: 1.5rem; 
    --space-1: 0.25rem; 
    --space-2: 0.5rem;
    --space-3: 0.75rem; 
    --space-4: 1rem; 
    --space-6: 1.5rem; 
    --space-8: 2rem;
    --radius-lg: 0.75rem; 
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1); 
    --transition: 200ms ease;
}

* { 
    margin: 0; 
    padding: 0; 
    box-sizing: border-box; 
}

body { 
    font-family: var(--font-sans); 
    font-size: var(--text-sm); 
    line-height: 1.5; 
    color: var(--neutral-700); 
    background: var(--neutral-50); 
}

.login-layout { 
    min-height: 100vh; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    padding: var(--space-6); 
    background: linear-gradient(135deg, #fff7ed 0%, var(--neutral-100) 100%); 
}

.login-card { 
    background: var(--neutral-0); 
    border-radius: var(--radius-lg); 
    box-shadow: var(--shadow-lg); 
    overflow: hidden; 
    width: 100%; 
    max-width: 420px; 
}

.login-header {
    padding: var(--space-8) var(--space-8) var(--space-6) var(--space-8);
    text-align: center;
    background: linear-gradient(180deg, var(--neutral-0) 0%, var(--neutral-50) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.brand-logo {
    height: 40px;
    margin-bottom: var(--space-6);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.welcome-title {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
    margin-top: var(--space-4);
}

.welcome-subtitle { 
    color: var(--neutral-500); 
    line-height: 1.6; 
}

.login-form { 
    padding: var(--space-8); 
}

.form-group { 
    margin-bottom: var(--space-6); 
}

.form-label { 
    display: block; 
    font-weight: 500; 
    color: var(--neutral-700); 
    margin-bottom: var(--space-2); 
}

.input-wrapper { 
    position: relative; 
}

.input-icon { 
    position: absolute; 
    left: var(--space-3); 
    top: 50%; 
    transform: translateY(-50%); 
    color: var(--neutral-400); 
    pointer-events: none; 
}

.form-input { 
    width: 100%; 
    padding: var(--space-3) var(--space-3) var(--space-3) 2.5rem; 
    border: 1px solid var(--neutral-300); 
    border-radius: var(--radius-lg); 
    transition: all var(--transition); 
    min-height: 2.75rem; 
}

.form-input:focus { 
    outline: none; 
    border-color: var(--primary-500); 
    box-shadow: 0 0 0 3px rgb(249 115 22 / 0.1); 
}

.password-toggle { 
    position: absolute; 
    right: var(--space-3); 
    top: 50%; 
    transform: translateY(-50%); 
    background: none; 
    border: none; 
    color: var(--neutral-400); 
    cursor: pointer; 
    padding: var(--space-1); 
    border-radius: 0.5rem; 
}

.submit-btn { 
    width: 100%; 
    background: var(--primary-600); 
    color: var(--neutral-0); 
    border: none; 
    padding: var(--space-4); 
    border-radius: var(--radius-lg); 
    font-weight: 500; 
    cursor: pointer; 
    transition: all var(--transition); 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    gap: var(--space-2); 
    margin-top: var(--space-8); 
}

.submit-btn:hover { 
    background: var(--primary-700); 
    transform: translateY(-1px); 
    box-shadow: var(--shadow-lg); 
}

.login-footer { 
    padding: var(--space-6) var(--space-8); 
    text-align: center; 
    background: var(--neutral-50); 
    border-top: 1px solid var(--neutral-200); 
}

.footer-text { 
    font-size: 0.75rem; 
    color: var(--neutral-500); 
}

.footer-link {
    color: var(--primary-600);
    text-decoration: none;
    font-weight: 500;
}

/* Logo styling classes */
.logo-responsive {
    max-width: 100%;
    height: auto;
    display: block;
}

.logo-login {
    height: 120px;
    width: auto;
    max-width: 300px;
    margin: 0 auto var(--space-6) auto;
    display: block;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
    transition: transform 0.3s ease, filter 0.3s ease;
}

.logo-login:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.2));
}

/* Responsive logo adjustments */
@media (max-width: 768px) {
    .logo-login {
        height: 100px;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .logo-login {
        height: 80px;
        max-width: 200px;
    }
}
