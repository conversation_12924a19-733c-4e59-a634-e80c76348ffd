<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NG Construction Safety Monitor</title>
    <link rel="icon" type="image/svg+xml" href="favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/dashboard.css">

</head>
<body>
    <div class="app-layout">
        <!-- Mobile Menu Toggle -->
        <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle mobile menu">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
            </svg>
        </button>

        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="image/logo.png" alt="NG Construction Safety Monitor" class="logo logo-sidebar logo-responsive">
                <button type="button" class="sidebar-toggle" id="sidebarToggle" aria-label="Toggle sidebar" title="Toggle sidebar">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
                    </svg>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section-title">Safety Monitoring</div>
                <ul class="nav-list">
                    <li><a href="#" class="nav-link active" data-section="overview">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><line x1="9" y1="9" x2="15" y2="9"/><line x1="9" y1="15" x2="15" y2="15"/>
                        </svg>
                        <span class="nav-text">Dashboard</span>
                    </a></li>
                    <li><a href="#" class="nav-link" data-section="upload">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        <span class="nav-text">Upload Video</span>
                    </a></li>
                    <li><a href="#" class="nav-link" data-section="analysis">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 12l2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                        </svg>
                        <span class="nav-text">Safety Analysis</span>
                    </a></li>
                    <li><a href="#" class="nav-link" data-section="documents">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/>
                        </svg>
                        <span class="nav-text">Safety Reports</span>
                    </a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="logout-container">
                    <button type="button" class="logout-btn" id="logoutBtn" aria-label="Logout" title="Logout">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/><polyline points="16,17 21,12 16,7"/><line x1="21" y1="12" x2="9" y2="12"/>
                        </svg>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-content">
                    <button type="button" class="back-button" id="backButton" aria-label="Go back" title="Go back">
                        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                            <path d="M19 12H5m7-7l-7 7 7 7"/>
                        </svg>
                    </button>
                    <div>
                        <h1 id="pageTitle" class="page-title">NG Construction Safety Dashboard</h1>
                        <p class="page-subtitle">Monitor construction site safety compliance and worker protection</p>
                    </div>
                    <div class="header-buttons">
                        <button type="button" class="btn btn-primary" id="quickUpload">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                            <span>Upload Construction Video</span>
                        </button>
                        <button type="button" class="logout-btn-header" id="logoutBtnHeader" aria-label="Logout from safety dashboard" title="Logout">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1-2-2h4"/>
                                <polyline points="16,17 21,12 16,7"/>
                                <line x1="21" y1="12" x2="9" y2="12"/>
                            </svg>
                            <span>Logout</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- Overview Section -->
            <section class="content-section active" id="overview-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-icon stat-icon-primary">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="23 7 16 12 23 17 23 7"/><rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                </svg>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="totalVideos">0</div>
                            <div class="stat-label">Construction Videos</div>
                            <div class="stat-description">Total site videos analyzed</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-icon stat-icon-success">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/>
                                </svg>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="totalWorkers">0</div>
                            <div class="stat-label">Workers Detected</div>
                            <div class="stat-description">Total construction workers identified</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-icon stat-icon-warning">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                    <line x1="12" y1="9" x2="12" y2="13"/><line x1="12" y1="17" x2="12.01" y2="17"/>
                                </svg>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="totalViolations">0</div>
                            <div class="stat-label">Safety Violations</div>
                            <div class="stat-description">Total safety equipment violations</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-icon stat-icon-primary">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/><path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                                    <line x1="9" y1="9" x2="9.01" y2="9"/><line x1="15" y1="9" x2="15.01" y2="9"/>
                                </svg>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="complianceRate">100%</div>
                            <div class="stat-label">Safety Compliance</div>
                            <div class="stat-description">Overall safety equipment compliance rate</div>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <h2 class="activity-title">Recent Safety Activity</h2>
                    <div id="activityList">
                        <!-- Activity items will be loaded by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Upload Section -->
            <section class="content-section" id="upload-section">
                <!-- Upload content will be loaded by JavaScript -->
            </section>

            <!-- Analysis Section -->
            <section class="content-section" id="analysis-section">
                <!-- Analysis content will be loaded by JavaScript -->
            </section>

            <!-- Documents Section -->
            <section class="content-section" id="documents-section">
                <!-- Documents content will be loaded by JavaScript -->
            </section>
        </main>
    </div>

    <script>
        // Construction Safety Dashboard - Enhanced JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const navLinks = document.querySelectorAll('.nav-link');
            const contentSections = document.querySelectorAll('.content-section');
            const pageTitle = document.getElementById('pageTitle');
            const pageSubtitle = document.querySelector('.page-subtitle');
            const logoutBtn = document.getElementById('logoutBtn');
            const logoutBtnHeader = document.getElementById('logoutBtnHeader');
            const quickUpload = document.getElementById('quickUpload');
            const backButton = document.getElementById('backButton');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');

            // Navigation history for back button
            let navigationHistory = ['overview'];
            let currentSection = 'overview';

            loadDashboardData();

            // Start polling for video status updates every 5 seconds
            setInterval(() => {
                // Only poll if we're on the analysis tab and have videos being analyzed
                const activeTab = document.querySelector('.tab-btn.active');
                if (activeTab && activeTab.dataset.tab === 'analysis') {
                    loadAnalysisSection();
                }
            }, 5000);

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    // Update back button and title positioning when sidebar is toggled
                    updateBackButtonPosition();

                    // Debug: Log the state
                    console.log('Sidebar collapsed:', sidebar.classList.contains('collapsed'));
                    console.log('Back button visible:', backButton.classList.contains('show'));
                });
            }

            // Mobile menu toggle
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                });
            }

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768) {
                    if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                        sidebar.classList.remove('open');
                    }
                }
            });

            function updateBackButtonPosition() {
                // This function ensures proper positioning after sidebar toggle
                // The CSS handles the positioning, but we can add any additional logic here if needed
                setTimeout(() => {
                    // Force a reflow to ensure CSS transitions work properly
                    backButton.offsetHeight;
                }, 10);
            }

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.dataset.section;
                    switchSection(section, true);
                });
            });

            if (quickUpload) {
                quickUpload.addEventListener('click', function() {
                    switchSection('upload', true);
                });
            }

            // Back button functionality
            if (backButton) {
                backButton.addEventListener('click', function() {
                    if (navigationHistory.length > 1) {
                        navigationHistory.pop(); // Remove current section
                        const previousSection = navigationHistory[navigationHistory.length - 1];
                        switchSection(previousSection, false);
                    }
                });
            }

            // Logout functionality for both buttons
            function handleLogout() {
                localStorage.clear();
                window.location.href = 'index.html';
            }

            if (logoutBtn) {
                logoutBtn.addEventListener('click', handleLogout);
            }

            if (logoutBtnHeader) {
                logoutBtnHeader.addEventListener('click', handleLogout);
            }

            function loadDashboardData() {
                fetch('/api/stats')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('totalVideos').textContent = data.total_videos || 0;
                        document.getElementById('totalWorkers').textContent = data.total_workers_detected || 0;
                        document.getElementById('totalViolations').textContent = data.total_safety_violations || 0;
                        document.getElementById('complianceRate').textContent = `${data.compliance_rate || 100}%`;
                    })
                    .catch(error => {
                        console.error('Error loading stats:', error);
                        document.getElementById('totalVideos').textContent = '0';
                        document.getElementById('totalWorkers').textContent = '0';
                        document.getElementById('totalViolations').textContent = '0';
                        document.getElementById('complianceRate').textContent = '100%';
                    });
                loadRecentActivity();
            }

            function switchSection(sectionName, addToHistory = true) {
                // Update navigation history
                if (addToHistory) {
                    if (currentSection !== sectionName) {
                        navigationHistory.push(sectionName);
                    }
                } else {
                    // When going back, don't add to history
                }

                currentSection = sectionName;

                // Show/hide back button
                if (sectionName === 'overview') {
                    backButton.classList.remove('show');
                } else {
                    backButton.classList.add('show');
                }

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.dataset.section === sectionName) {
                        link.classList.add('active');
                    }
                });

                contentSections.forEach(section => {
                    section.classList.remove('active');
                });

                const targetSection = document.getElementById(`${sectionName}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                }

                const titles = {
                    overview: { title: 'NG Construction Safety Dashboard', subtitle: 'Monitor construction site safety compliance and worker protection' },
                    upload: { title: 'Upload Construction Video', subtitle: 'Upload videos for AI-powered safety analysis' },
                    analysis: { title: 'Safety Analysis Results', subtitle: 'View and manage your analyzed construction videos' },
                    documents: { title: 'Safety Reports', subtitle: 'Access generated safety compliance documents' }
                };

                const titleData = titles[sectionName] || titles.overview;
                pageTitle.textContent = titleData.title;
                if (pageSubtitle) {
                    pageSubtitle.textContent = titleData.subtitle;
                }

                if (sectionName === 'upload') {
                    loadUploadSection();
                } else if (sectionName === 'analysis') {
                    loadAnalysisSection();
                } else if (sectionName === 'documents') {
                    loadDocumentsSection();
                }
            }

            function loadUploadSection() {
                const uploadSection = document.getElementById('upload-section');
                uploadSection.innerHTML = `
                    <div class="stat-card">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem;">Upload Construction Video for Safety Analysis</h3>
                        <p style="color: var(--neutral-500); margin-bottom: 2rem;">Upload a construction site video or provide video path to analyze worker safety compliance</p>

                        <!-- Upload Method Selection -->
                        <div style="margin-bottom: 2rem;">
                            <label style="display: block; font-weight: 500; margin-bottom: 1rem;">Upload Method</label>
                            <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="radio" name="uploadMethod" value="file" checked style="margin: 0;">
                                    <span>Upload Video File</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="radio" name="uploadMethod" value="path" style="margin: 0;">
                                    <span>Video Path/URL</span>
                                </label>
                            </div>
                        </div>

                        <form id="videoUploadForm">
                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem;">Video Title</label>
                                <input type="text" id="videoTitle" class="form-input" placeholder="Enter construction site description" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--neutral-300); border-radius: 0.5rem;">
                            </div>

                            <!-- File Upload Section -->
                            <div id="fileUploadSection" style="margin-bottom: 1.5rem;">
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem;">Construction Video File</label>
                                <div class="file-upload-area" id="fileUploadArea" style="border: 2px dashed var(--neutral-300); border-radius: 0.75rem; padding: 3rem 2rem; text-align: center; cursor: pointer; transition: all 0.2s;">
                                    <div style="margin-bottom: 1rem;">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="color: var(--neutral-400);">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="7,10 12,15 17,10"></polyline>
                                            <line x1="12" y1="15" x2="12" y2="3"></line>
                                        </svg>
                                    </div>
                                    <p style="font-weight: 500; margin-bottom: 0.25rem;">Drop construction video here or click to browse</p>
                                    <p style="font-size: 0.875rem; color: var(--neutral-500);">Supports MP4, AVI, MOV files up to 100MB</p>
                                </div>
                                <input type="file" id="videoFile" accept="video/*" style="display: none;">
                            </div>

                            <!-- Path Upload Section -->
                            <div id="pathUploadSection" style="margin-bottom: 1.5rem; display: none;">
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem;">Video Path or URL</label>
                                <input type="text" id="videoPath" class="form-input" placeholder="Enter video file path or URL (e.g., C:\\Videos\\construction.mp4)" style="width: 100%; padding: 0.75rem; border: 1px solid var(--neutral-300); border-radius: 0.5rem;">
                                <p style="font-size: 0.75rem; color: var(--neutral-500); margin-top: 0.5rem;">
                                    Examples: C:\\Videos\\site.mp4, /home/<USER>/videos/construction.mp4, https://example.com/video.mp4
                                </p>
                            </div>

                            <button type="submit" class="btn btn-primary" style="width: 100%;">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="7,10 12,15 17,10"></polyline>
                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                </svg>
                                <span id="uploadButtonText">Upload & Analyze for Safety Compliance</span>
                            </button>
                        </form>
                    </div>
                `;
                initializeUploadForm();
            }

            function initializeUploadForm() {
                const form = document.getElementById('videoUploadForm');
                const fileInput = document.getElementById('videoFile');
                const uploadArea = document.getElementById('fileUploadArea');
                const fileUploadSection = document.getElementById('fileUploadSection');
                const pathUploadSection = document.getElementById('pathUploadSection');
                const uploadButtonText = document.getElementById('uploadButtonText');
                const uploadMethodRadios = document.querySelectorAll('input[name="uploadMethod"]');

                // Handle upload method change
                uploadMethodRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'file') {
                            fileUploadSection.style.display = 'block';
                            pathUploadSection.style.display = 'none';
                            uploadButtonText.textContent = 'Upload & Analyze for Safety Compliance';
                        } else if (this.value === 'path') {
                            fileUploadSection.style.display = 'none';
                            pathUploadSection.style.display = 'block';
                            uploadButtonText.textContent = 'Analyze Video from Path';
                        }
                    });
                });

                uploadArea.addEventListener('click', () => fileInput.click());

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        const file = e.target.files[0];
                        uploadArea.innerHTML = `<p style="color: var(--safety-green); font-weight: 500;">✅ ${file.name} selected (${formatFileSize(file.size)})</p>`;
                    }
                });

                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    handleVideoUpload();
                });
            }

            function handleVideoUpload() {
                const title = document.getElementById('videoTitle').value;
                const uploadMethod = document.querySelector('input[name="uploadMethod"]:checked').value;

                let requestData, requestOptions;

                if (uploadMethod === 'file') {
                    const fileInput = document.getElementById('videoFile');
                    const file = fileInput.files[0];

                    if (!file) {
                        showToast('Please select a construction site video', 'error');
                        return;
                    }

                    showToast('Uploading construction video for safety analysis...', 'info');

                    requestData = new FormData();
                    requestData.append('video', file);
                    requestData.append('title', title);
                    requestData.append('upload_method', 'file');

                    requestOptions = {
                        method: 'POST',
                        body: requestData
                    };
                } else if (uploadMethod === 'path') {
                    const videoPath = document.getElementById('videoPath').value.trim();

                    if (!videoPath) {
                        showToast('Please enter a video path or URL', 'error');
                        return;
                    }

                    showToast('Processing video from path for safety analysis...', 'info');

                    requestData = JSON.stringify({
                        title: title,
                        video_path: videoPath,
                        upload_method: 'path'
                    });

                    requestOptions = {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: requestData
                    };
                }

                fetch('/api/upload', requestOptions)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('Video processing started! Safety analysis in progress...', 'success');
                        loadDashboardData();
                        document.getElementById('videoUploadForm').reset();

                        // Reset upload method to file
                        document.querySelector('input[name="uploadMethod"][value="file"]').checked = true;
                        document.getElementById('fileUploadSection').style.display = 'block';
                        document.getElementById('pathUploadSection').style.display = 'none';
                        document.getElementById('uploadButtonText').textContent = 'Upload & Analyze for Safety Compliance';

                        setTimeout(() => switchSection('analysis', true), 2000);
                    } else {
                        showToast(`Processing failed: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('Upload error:', error);
                    showToast('Processing failed. Please try again.', 'error');
                });
            }

            // Store previous video states to detect completion
            let previousVideoStates = {};

            function loadAnalysisSection() {
                const analysisSection = document.getElementById('analysis-section');

                fetch('/api/videos')
                    .then(response => response.json())
                    .then(videos => {
                        // Check for newly completed analyses
                        videos.forEach(video => {
                            const previousState = previousVideoStates[video.id];
                            const currentState = video.status;

                            // If video was analyzing and now completed, show success modal
                            if (previousState === 'analyzing' && currentState === 'completed') {
                                showAnalysisCompletionModal(video);
                            }

                            // Update state tracking
                            previousVideoStates[video.id] = currentState;
                        });
                        if (videos.length === 0) {
                            analysisSection.innerHTML = `
                                <div class="stat-card" style="text-align: center; padding: 3rem;">
                                    <h3 style="margin-bottom: 0.5rem;">No construction videos uploaded yet</h3>
                                    <p style="color: var(--neutral-500);">Upload a construction site video to start safety analysis</p>
                                </div>
                            `;
                            return;
                        }

                        analysisSection.innerHTML = videos.map(video => `
                            <div class="stat-card" style="margin-bottom: 1rem;" id="video-${video.id}">
                                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                                    <div style="flex: 1;">
                                        <h3 style="margin-bottom: 0.5rem;">${video.title}</h3>
                                        <p style="color: var(--neutral-500); font-size: 0.875rem;">File: ${video.filename}</p>
                                        <p style="color: var(--neutral-500); font-size: 0.875rem;">Uploaded: ${new Date(video.upload_date).toLocaleDateString()}</p>
                                        ${video.analysis_error ?
                                            `<p style="color: var(--danger-red); font-size: 0.75rem; margin-top: 0.5rem;">Error: ${video.analysis_error}</p>` :
                                            ''
                                        }
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 1rem;">
                                        ${video.status === 'failed' ?
                                            `<div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="background: rgb(239 68 68 / 0.1); color: var(--danger-red); padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">❌ Analysis Failed</span>
                                                <button type="button" onclick="retryAnalysis('${video.id}')" title="Retry Analysis" style="background: var(--primary-600); color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 0.25rem; cursor: pointer; font-size: 0.75rem;">
                                                    🔄 Retry
                                                </button>
                                            </div>` :
                                            video.status === 'completed' ?
                                                '<span style="background: rgb(22 163 74 / 0.1); color: var(--safety-green); padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">✅ Safety Analysis Complete</span>' :
                                                '<span style="background: rgb(245 158 11 / 0.1); color: var(--warning-orange); padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">⏳ Analyzing Safety...</span>'
                                        }
                                        <button type="button" class="delete-btn" onclick="deleteVideo('${video.id}')" title="Delete Video" style="background: var(--danger-red); color: white; border: none; padding: 0.5rem; border-radius: 0.375rem; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s;">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="3,6 5,6 21,6"></polyline>
                                                <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                                <line x1="14" y1="11" x2="14" y2="17"></line>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                    })
                    .catch(error => {
                        console.error('Error loading videos:', error);
                        analysisSection.innerHTML = '<div class="stat-card"><p>Error loading analysis results</p></div>';
                    });
            }

            function loadDocumentsSection() {
                const documentsSection = document.getElementById('documents-section');

                fetch('/api/documents')
                    .then(response => response.json())
                    .then(documents => {
                        if (documents.length === 0) {
                            documentsSection.innerHTML = `
                                <div class="stat-card" style="text-align: center; padding: 3rem;">
                                    <h3 style="margin-bottom: 0.5rem;">No safety reports available</h3>
                                    <p style="color: var(--neutral-500);">Analyze construction videos to generate safety compliance reports</p>
                                </div>
                            `;
                            return;
                        }

                        documentsSection.innerHTML = documents.map(doc => `
                            <div class="stat-card" style="margin-bottom: 1rem;" id="document-${doc.id}">
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <div style="width: 48px; height: 48px; background: #fff7ed; border-radius: 0.75rem; display: flex; align-items: center; justify-content: center; color: var(--primary-600);">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                            <polyline points="14,2 14,8 20,8"></polyline>
                                        </svg>
                                    </div>
                                    <div style="flex: 1; cursor: pointer;" onclick="openDocument('${doc.id}')">
                                        <h3 style="margin-bottom: 0.25rem;">${doc.title}</h3>
                                        <p style="color: var(--neutral-500); font-size: 0.875rem;">Created: ${new Date(doc.created_date).toLocaleDateString()}</p>
                                        <p style="color: var(--neutral-500); font-size: 0.875rem;">Type: SAFETY COMPLIANCE REPORT</p>
                                    </div>
                                    <div style="display: flex; gap: 0.5rem;">
                                        ${doc.has_pdf ?
                                            `<button type="button" onclick="viewDocumentPDF('${doc.id}')" title="View PDF Report" style="background: #059669; color: white; border: none; padding: 0.5rem; border-radius: 0.375rem; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s;">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                    <circle cx="12" cy="12" r="3"></circle>
                                                </svg>
                                            </button>
                                            <button type="button" onclick="downloadDocumentPDF('${doc.id}', '${doc.title}')" title="Download PDF Report" style="background: #dc2626; color: white; border: none; padding: 0.5rem; border-radius: 0.375rem; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s;">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                    <polyline points="7,10 12,15 17,10"></polyline>
                                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                                </svg>
                                            </button>` :
                                            `<button type="button" onclick="openDocument('${doc.id}')" title="View Text Report" style="background: var(--primary-600); color: white; border: none; padding: 0.5rem; border-radius: 0.375rem; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s;">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                    <circle cx="12" cy="12" r="3"></circle>
                                                </svg>
                                            </button>
                                            <button type="button" onclick="downloadDocument('${doc.id}', '${doc.title}')" title="Download Text Report" style="background: #059669; color: white; border: none; padding: 0.5rem; border-radius: 0.375rem; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s;">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                    <polyline points="7,10 12,15 17,10"></polyline>
                                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                                </svg>
                                            </button>`
                                        }
                                        <button type="button" class="delete-btn" onclick="deleteDocument('${doc.id}')" title="Delete Document" style="background: var(--danger-red); color: white; border: none; padding: 0.5rem; border-radius: 0.375rem; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s;">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="3,6 5,6 21,6"></polyline>
                                            <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                                            <line x1="10" y1="11" x2="10" y2="17"></line>
                                            <line x1="14" y1="11" x2="14" y2="17"></line>
                                        </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                    })
                    .catch(error => {
                        console.error('Error loading documents:', error);
                        documentsSection.innerHTML = '<div class="stat-card"><p>Error loading safety reports</p></div>';
                    });
            }

            function loadRecentActivity() {
                const activityList = document.getElementById('activityList');

                Promise.all([
                    fetch('/api/videos').then(r => r.json()).catch(() => []),
                    fetch('/api/documents').then(r => r.json()).catch(() => [])
                ]).then(([videos, documents]) => {
                    let activities = [];

                    videos.forEach(video => {
                        activities.push({
                            type: 'upload',
                            message: `Construction video "${video.title}" uploaded for safety analysis`,
                            time: video.upload_date
                        });
                    });

                    documents.forEach(doc => {
                        activities.push({
                            type: 'analysis',
                            message: `Safety compliance report generated: "${doc.title}"`,
                            time: doc.created_date
                        });
                    });

                    activities.sort((a, b) => new Date(b.time) - new Date(a.time));

                    if (activities.length === 0) {
                        activityList.innerHTML = `
                            <div style="display: flex; align-items: center; gap: 0.75rem; padding: 1rem;">
                                <div style="width: 32px; height: 32px; background: #fff7ed; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: var(--primary-600);">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <polyline points="12 6 12 12 16 14"></polyline>
                                    </svg>
                                </div>
                                <div>
                                    <p style="margin-bottom: 0.25rem;">Welcome to Construction Safety Monitor!</p>
                                    <span style="font-size: 0.75rem; color: var(--neutral-500);">Upload your first construction video to start safety analysis</span>
                                </div>
                            </div>
                        `;
                        return;
                    }

                    activityList.innerHTML = activities.slice(0, 5).map(activity => `
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 1rem; border-bottom: 1px solid var(--neutral-200);">
                            <div style="width: 32px; height: 32px; background: #fff7ed; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: var(--primary-600);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    ${activity.type === 'upload' ?
                                        '<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7,10 12,15 17,10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line>' :
                                        '<circle cx="12" cy="12" r="3"></circle><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>'
                                    }
                                </svg>
                            </div>
                            <div>
                                <p style="margin-bottom: 0.25rem;">${activity.message}</p>
                                <span style="font-size: 0.75rem; color: var(--neutral-500);">${formatTimeAgo(activity.time)}</span>
                            </div>
                        </div>
                    `).join('');
                });
            }

            window.openDocument = function(docId) {
                fetch(`/api/documents/${docId}`)
                    .then(response => response.json())
                    .then(document => {
                        showWorkerReportModal(document.title, document.content);
                    })
                    .catch(error => {
                        console.error('Error loading document:', error);
                        showToast('❌ Error loading worker report', 'error');
                    });
            };

            // Custom modal for worker reports
            function showWorkerReportModal(title, content) {
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.5); display: flex; align-items: center;
                    justify-content: center; z-index: 10000;
                `;

                const modalContent = document.createElement('div');
                modalContent.style.cssText = `
                    background: white; border-radius: 12px; padding: 24px;
                    max-width: 800px; max-height: 80vh; overflow-y: auto;
                    box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1);
                `;

                modalContent.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #f97316; padding-bottom: 12px;">
                        <h2 style="color: #f97316; margin: 0; font-size: 24px; font-weight: 700;">🏗️ Worker Reports</h2>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">×</button>
                    </div>
                    <h3 style="color: #334155; margin-bottom: 16px;">${title}</h3>
                    <pre style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.6; color: #475569; background: #f8fafc; padding: 16px; border-radius: 8px; border-left: 4px solid #f97316;">${content}</pre>
                `;

                modal.className = 'modal';
                modal.appendChild(modalContent);
                document.body.appendChild(modal);

                // Close on background click
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) modal.remove();
                });
            }

            // Delete video function
            window.deleteVideo = function(videoId) {
                if (confirm('Are you sure you want to delete this video and its analysis results? This action cannot be undone.')) {
                    fetch(`/api/videos/${videoId}`, {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast('Video deleted successfully', 'success');
                            // Remove the video card from the UI
                            const videoCard = document.getElementById(`video-${videoId}`);
                            if (videoCard) {
                                videoCard.remove();
                            }
                            // Refresh dashboard data
                            loadDashboardData();
                            // If no videos left, show empty state
                            setTimeout(() => {
                                loadAnalysisSection();
                            }, 500);
                        } else {
                            showToast(`Failed to delete video: ${data.error}`, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting video:', error);
                        showToast('Error deleting video. Please try again.', 'error');
                    });
                }
            };

            // Delete document function
            window.deleteDocument = function(docId) {
                if (confirm('Are you sure you want to delete this safety report? This action cannot be undone.')) {
                    fetch(`/api/documents/${docId}`, {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast('Document deleted successfully', 'success');
                            // Remove the document card from the UI
                            const docCard = document.getElementById(`document-${docId}`);
                            if (docCard) {
                                docCard.remove();
                            }
                            // If no documents left, show empty state
                            setTimeout(() => {
                                loadDocumentsSection();
                            }, 500);
                        } else {
                            showToast(`Failed to delete document: ${data.error}`, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting document:', error);
                        showToast('Error deleting document. Please try again.', 'error');
                    });
                }
            };

            // Retry analysis function
            window.retryAnalysis = function(videoId) {
                if (confirm('Retry safety analysis for this video?')) {
                    fetch(`/api/videos/${videoId}/retry`, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast('Analysis restarted! Processing video...', 'success');
                            // Refresh the analysis section to show updated status
                            setTimeout(() => {
                                loadAnalysisSection();
                            }, 1000);
                        } else {
                            showToast(`Failed to retry analysis: ${data.error}`, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error retrying analysis:', error);
                        showToast('Error retrying analysis. Please try again.', 'error');
                    });
                }
            };

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            function formatTimeAgo(dateString) {
                const now = new Date();
                const date = new Date(dateString);
                const diffInSeconds = Math.floor((now - date) / 1000);

                if (diffInSeconds < 60) return 'Just now';
                if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
                if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
                return `${Math.floor(diffInSeconds / 86400)} days ago`;
            }

            function showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 1rem;
                    right: 1rem;
                    background: var(--neutral-0);
                    border: 1px solid var(--neutral-200);
                    border-radius: 0.75rem;
                    padding: 1rem;
                    box-shadow: var(--shadow-lg);
                    z-index: 1000;
                    max-width: 400px;
                    border-left: 4px solid ${type === 'success' ? 'var(--safety-green)' : type === 'error' ? 'var(--danger-red)' : 'var(--primary-500)'};
                `;
                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.remove();
                }, 3000);
            }

            // Analysis completion modal functions
            window.showAnalysisCompletionModal = function(videoData) {
                const modal = document.getElementById('analysisModal');
                const videoNameEl = document.getElementById('modalVideoName');
                const analysisDateEl = document.getElementById('modalAnalysisDate');

                // Store video data for later use
                window.completedVideoData = videoData;

                // Update modal content
                videoNameEl.textContent = videoData.title || 'Unknown Video';
                analysisDateEl.textContent = new Date().toLocaleDateString();

                // Show modal
                modal.style.display = 'block';

                // Add click outside to close
                modal.onclick = function(event) {
                    if (event.target === modal) {
                        closeAnalysisModal();
                    }
                };
            };

            window.closeAnalysisModal = function() {
                const modal = document.getElementById('analysisModal');
                modal.style.display = 'none';
            };

            window.viewSafetyReport = function() {
                closeAnalysisModal();

                // Switch to documents tab
                const documentsTab = document.querySelector('[data-tab="documents"]');
                if (documentsTab) {
                    documentsTab.click();
                }

                // Refresh documents to show the latest report
                setTimeout(() => {
                    loadDocuments();
                    showToast('📊 Safety report is ready for viewing and download!', 'success');
                }, 500);
            };

            // Download text document function
            window.downloadDocument = function(docId, docTitle) {
                // Create a temporary link to trigger download
                const link = document.createElement('a');
                link.href = `/api/documents/${docId}/download`;
                link.download = `${docTitle}.txt`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showToast('📥 Text report downloaded successfully!', 'success');
            };

            // Download PDF document function
            window.downloadDocumentPDF = function(docId, docTitle) {
                // Create a temporary link to trigger download
                const link = document.createElement('a');
                link.href = `/api/documents/${docId}/download-pdf`;
                link.download = `${docTitle}.pdf`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showToast('📥 PDF report downloaded successfully!', 'success');
            };

            // View PDF document function
            window.viewDocumentPDF = function(docId) {
                // Open PDF in new tab
                window.open(`/api/documents/${docId}/view-pdf`, '_blank');
            };
        });
    </script>

    <!-- Analysis Completion Modal -->
    <div id="analysisModal" class="analysis-modal">
        <div class="analysis-modal-content">
            <div class="modal-header">
                <div class="modal-icon">
                    <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h3 class="modal-title">Analysis Successfully Completed!</h3>
                <p class="modal-subtitle">Your construction video has been analyzed for safety compliance</p>
            </div>

            <div class="modal-body">
                <p class="modal-body-text">
                    🎉 Safety analysis report is now ready for review
                </p>
                <div class="modal-info-container">
                    <div class="modal-info-row">
                        <span class="modal-info-label">Video:</span>
                        <span id="modalVideoName" class="modal-info-value"></span>
                    </div>
                    <div class="modal-info-row">
                        <span class="modal-info-label">Analysis Date:</span>
                        <span id="modalAnalysisDate" class="modal-info-value"></span>
                    </div>
                </div>
            </div>

            <div class="modal-actions">
                <button class="modal-btn modal-btn-secondary" onclick="closeAnalysisModal()">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    Close
                </button>
                <button class="modal-btn modal-btn-primary" onclick="viewSafetyReport()">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    View & Download Report
                </button>
            </div>
        </div>
    </div>
</body>
</html>
