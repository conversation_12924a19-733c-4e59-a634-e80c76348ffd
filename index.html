<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Construction Safety Monitor - Login</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-500: #f97316; --primary-600: #ea580c; --primary-700: #c2410c;
            --neutral-0: #ffffff; --neutral-50: #f8fafc; --neutral-100: #f1f5f9;
            --neutral-200: #e2e8f0; --neutral-300: #cbd5e1; --neutral-400: #94a3b8;
            --neutral-500: #64748b; --neutral-700: #334155; --neutral-900: #0f172a;
            --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --text-sm: 0.875rem; --text-2xl: 1.5rem; --space-1: 0.25rem; --space-2: 0.5rem;
            --space-3: 0.75rem; --space-4: 1rem; --space-6: 1.5rem; --space-8: 2rem;
            --radius-lg: 0.75rem; --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1); --transition: 200ms ease;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: var(--font-sans); font-size: var(--text-sm); line-height: 1.5; color: var(--neutral-700); background: var(--neutral-50); }
        .login-layout { min-height: 100vh; display: flex; align-items: center; justify-content: center; padding: var(--space-6); background: linear-gradient(135deg, #fff7ed 0%, var(--neutral-100) 100%); }
        .login-card { background: var(--neutral-0); border-radius: var(--radius-lg); box-shadow: var(--shadow-lg); overflow: hidden; width: 100%; max-width: 420px; }
        .login-header { padding: var(--space-8); text-align: center; background: linear-gradient(180deg, var(--neutral-0) 0%, var(--neutral-50) 100%); }
        .brand-logo {
            height: 40px;
            margin-bottom: var(--space-6);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        .welcome-title { font-size: var(--text-2xl); font-weight: 700; color: var(--neutral-900); margin-bottom: var(--space-2); }
        .welcome-subtitle { color: var(--neutral-500); line-height: 1.6; }
        .login-form { padding: var(--space-8); }
        .form-group { margin-bottom: var(--space-6); }
        .form-label { display: block; font-weight: 500; color: var(--neutral-700); margin-bottom: var(--space-2); }
        .input-wrapper { position: relative; }
        .input-icon { position: absolute; left: var(--space-3); top: 50%; transform: translateY(-50%); color: var(--neutral-400); pointer-events: none; }
        .form-input { width: 100%; padding: var(--space-3) var(--space-3) var(--space-3) 2.5rem; border: 1px solid var(--neutral-300); border-radius: var(--radius-lg); transition: all var(--transition); min-height: 2.75rem; }
        .form-input:focus { outline: none; border-color: var(--primary-500); box-shadow: 0 0 0 3px rgb(249 115 22 / 0.1); }
        .password-toggle { position: absolute; right: var(--space-3); top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--neutral-400); cursor: pointer; padding: var(--space-1); border-radius: 0.5rem; }
        .submit-btn { width: 100%; background: var(--primary-600); color: var(--neutral-0); border: none; padding: var(--space-4); border-radius: var(--radius-lg); font-weight: 500; cursor: pointer; transition: all var(--transition); display: flex; align-items: center; justify-content: center; gap: var(--space-2); margin-top: var(--space-8); }
        .submit-btn:hover { background: var(--primary-700); transform: translateY(-1px); box-shadow: var(--shadow-lg); }
        .login-footer { padding: var(--space-6) var(--space-8); text-align: center; background: var(--neutral-50); border-top: 1px solid var(--neutral-200); }
        .footer-text { font-size: 0.75rem; color: var(--neutral-500); }
        .footer-link { color: var(--primary-600); text-decoration: none; font-weight: 500; }
    </style>
</head>
<body>
    <div class="login-layout">
        <div class="login-card">
            <div class="login-header">
                <svg class="brand-logo" viewBox="0 0 140 40" fill="none">
                    <rect width="140" height="40" rx="12" fill="url(#gradient)" stroke="url(#borderGradient)" stroke-width="1"/>
                    <text x="70" y="26" text-anchor="middle" fill="white" font-family="Inter" font-weight="700" font-size="13" letter-spacing="0.5px">SAFETY MONITOR</text>
                    <defs>
                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#f97316"/>
                            <stop offset="50%" stop-color="#ea580c"/>
                            <stop offset="100%" stop-color="#c2410c"/>
                        </linearGradient>
                        <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#fed7aa"/>
                            <stop offset="100%" stop-color="#fdba74"/>
                        </linearGradient>
                    </defs>
                </svg>
                
                <div class="welcome-content">
                    <h1 class="welcome-title">Construction Safety Monitor</h1>
                    <p class="welcome-subtitle">AI-powered construction site safety analysis system</p>
                </div>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">Safety Manager ID</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        <input type="text" id="username" name="username" class="form-input" required placeholder="Enter safety manager ID" autocomplete="username">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                            <circle cx="12" cy="16" r="1"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                        <input type="password" id="password" name="password" class="form-input" required placeholder="Enter your password" autocomplete="current-password">
                        <button type="button" class="password-toggle" id="togglePassword" aria-label="Show or hide password">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    <span>Access Safety Dashboard</span>
                </button>
            </form>
            
            <div class="login-footer">
                <p class="footer-text">
                    By signing in, you agree to our 
                    <a href="#" class="footer-link">Safety Policies</a> and 
                    <a href="#" class="footer-link">Terms of Use</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        // Construction Safety Login - Optimized JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Check if already logged in
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard.html';
            }

            const loginForm = document.getElementById('loginForm');
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');

            // Password toggle
            if (togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    
                    const icon = this.querySelector('svg');
                    if (type === 'text') {
                        icon.innerHTML = '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line>';
                    } else {
                        icon.innerHTML = '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle>';
                    }
                });
            }

            // Form submission
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    localStorage.setItem('isLoggedIn', 'true');
                    window.location.href = 'dashboard.html';
                });
            }
        });
    </script>
</body>
</html>
