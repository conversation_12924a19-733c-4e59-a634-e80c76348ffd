<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Construction Safety Monitor - Login</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/login.css">
</head>
<body>
    <div class="login-layout">
        <div class="login-card">
            <div class="login-header">
                <svg class="brand-logo" viewBox="0 0 140 40" fill="none">
                    <rect width="140" height="40" rx="12" fill="url(#gradient)" stroke="url(#borderGradient)" stroke-width="1"/>
                    <text x="70" y="26" text-anchor="middle" fill="white" font-family="Inter" font-weight="700" font-size="13" letter-spacing="0.5px">SAFETY MONITOR</text>
                    <defs>
                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#f97316"/>
                            <stop offset="50%" stop-color="#ea580c"/>
                            <stop offset="100%" stop-color="#c2410c"/>
                        </linearGradient>
                        <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#fed7aa"/>
                            <stop offset="100%" stop-color="#fdba74"/>
                        </linearGradient>
                    </defs>
                </svg>
                
                <div class="welcome-content">
                    <h1 class="welcome-title">Construction Safety Monitor</h1>
                    <p class="welcome-subtitle">AI-powered construction site safety analysis system</p>
                </div>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">Safety Manager ID</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        <input type="text" id="username" name="username" class="form-input" required placeholder="Enter safety manager ID" autocomplete="username">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                            <circle cx="12" cy="16" r="1"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                        <input type="password" id="password" name="password" class="form-input" required placeholder="Enter your password" autocomplete="current-password">
                        <button type="button" class="password-toggle" id="togglePassword" aria-label="Show or hide password">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    <span>Access Safety Dashboard</span>
                </button>
            </form>
            
            <div class="login-footer">
                <p class="footer-text">
                    By signing in, you agree to our 
                    <a href="#" class="footer-link">Safety Policies</a> and 
                    <a href="#" class="footer-link">Terms of Use</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        // Construction Safety Login - Optimized JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Check if already logged in
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard.html';
            }

            const loginForm = document.getElementById('loginForm');
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');

            // Password toggle
            if (togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    
                    const icon = this.querySelector('svg');
                    if (type === 'text') {
                        icon.innerHTML = '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line>';
                    } else {
                        icon.innerHTML = '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle>';
                    }
                });
            }

            // Form submission
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    localStorage.setItem('isLoggedIn', 'true');
                    window.location.href = 'dashboard.html';
                });
            }
        });
    </script>
</body>
</html>
