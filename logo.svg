<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="20" cy="20" r="18" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="2"/>
  
  <!-- Video Icon -->
  <path d="M14 13L14 27C14 27.5523 14.4477 28 15 28L23 28C23.5523 28 24 27.5523 24 27L24 13C24 12.4477 23.5523 12 23 12L15 12C14.4477 12 14 12.4477 14 13Z" fill="white" fill-opacity="0.9"/>
  
  <!-- Play Button -->
  <path d="M17.5 16.5L17.5 23.5C17.5 23.7761 17.7761 24 18.0522 23.8944L22.5 21.8944C22.7761 21.7889 22.7761 21.2111 22.5 21.1056L18.0522 19.1056C17.7761 19 17.5 19.2239 17.5 19.5Z" fill="url(#gradient3)"/>
  
  <!-- Vault Lines -->
  <path d="M26 14L30 14" stroke="url(#gradient4)" stroke-width="2" stroke-linecap="round"/>
  <path d="M26 20L32 20" stroke="url(#gradient4)" stroke-width="2" stroke-linecap="round"/>
  <path d="M26 26L30 26" stroke="url(#gradient4)" stroke-width="2" stroke-linecap="round"/>
  
  <!-- Text -->
  <text x="40" y="16" font-family="Inter, sans-serif" font-size="14" font-weight="700" fill="url(#textGradient)">VideoVault</text>
  <text x="40" y="28" font-family="Inter, sans-serif" font-size="8" font-weight="400" fill="#64748B">AI Video Analysis</text>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="2" y1="2" x2="38" y2="38" gradientUnits="userSpaceOnUse">
      <stop stop-color="#0EA5E9"/>
      <stop offset="1" stop-color="#0369A1"/>
    </linearGradient>
    
    <linearGradient id="gradient2" x1="2" y1="2" x2="38" y2="38" gradientUnits="userSpaceOnUse">
      <stop stop-color="#38BDF8"/>
      <stop offset="1" stop-color="#0284C7"/>
    </linearGradient>
    
    <linearGradient id="gradient3" x1="17.5" y1="16.5" x2="22.5" y2="23.5" gradientUnits="userSpaceOnUse">
      <stop stop-color="#0EA5E9"/>
      <stop offset="1" stop-color="#0369A1"/>
    </linearGradient>
    
    <linearGradient id="gradient4" x1="26" y1="14" x2="32" y2="26" gradientUnits="userSpaceOnUse">
      <stop stop-color="#38BDF8"/>
      <stop offset="1" stop-color="#0284C7"/>
    </linearGradient>
    
    <linearGradient id="textGradient" x1="40" y1="12" x2="110" y2="20" gradientUnits="userSpaceOnUse">
      <stop stop-color="#0F172A"/>
      <stop offset="1" stop-color="#334155"/>
    </linearGradient>
  </defs>
</svg>
