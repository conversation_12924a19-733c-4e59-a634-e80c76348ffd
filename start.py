import os, sys, subprocess, webbrowser, time, threading

def install_dependencies():
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Flask", "Flask-CORS", "opencv-python", "numpy", "Werkzeug", "yt-dlp", "requests", "reportlab"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies - run: pip install -r requirements.txt")

def open_browser():
    time.sleep(3); webbrowser.open('http://localhost:5000')

def main():
    print("🏗️ Construction Safety Monitoring System\n" + "="*50)
    install_dependencies()
    for directory in ['uploads', 'analysis_results', 'documents']:
        os.makedirs(directory, exist_ok=True)
    print("🚀 Starting server at http://localhost:5000")
    threading.Thread(target=open_browser, daemon=True).start()
    try:
        from construction_app import app
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 Stopped!")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__": main()
